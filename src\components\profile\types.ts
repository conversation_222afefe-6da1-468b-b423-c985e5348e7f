export interface UserProps {
  fullName: string;
  staffId: string;
  role: {
    name: string;
    permissions?: { name: string }[];
  };
  roles?: {
    name: string;
    permissions: { name: string }[];
  }[];
  createdAt: Date;
  emailAddress: string;
  designation: string;
  lastLogin: string;
  isActive: boolean;
  dateResetPasswordRequest: Date;
  dateResetPassword: Date;
}

export interface MenuItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}
