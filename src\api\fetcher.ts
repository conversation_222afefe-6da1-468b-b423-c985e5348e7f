import axios from 'axios';
import { toast } from 'sonner';
import { snapshot } from 'valtio';
import { accessTokenStore } from '@/store/accessToken';
import { Logout } from '@/lib/utils';

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: Record<string, any>;
  request?: any;
}

interface ApiError {
  response?: ApiResponse;
  message: string;
  config: Record<string, any>;
  code?: string;
  request?: any;
}

export const api = process.env.NEXT_PUBLIC_CEDARCREST_API_V1;

export const myApi = axios.create({
  baseURL: api,
  headers: { 'Content-Type': 'application/json' },
});

myApi.interceptors.request.use(
  (config) => {
    const accessToken = snapshot(accessTokenStore).accessToken;
    const newConfig = { ...config };
    newConfig.headers = newConfig.headers || {};

    if (accessToken) {
      newConfig.headers.Authorization = `Bearer ${accessToken}`;
    }

    return newConfig;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor
myApi.interceptors.response.use(
  function (response) {
    return response;
  },
  async function (error) {
    if (error.response?.status === 400) {
      toast.error(error.response?.data.message);
      return Promise.reject(error.response?.data);
    }
    if (error.response?.status === 401) {
      Logout();
    }

    if (error.response?.status === 404) {
      toast.error(error.response?.data.message);
      return Promise.reject(error.response?.data);
    }

    if (error.response?.status === 403) {
      toast.error("Sorry! You're not authorized to perform this action");
      return Promise.reject(
        "Sorry! You're not authorized to perform this action"
      );
    }

    return Promise.reject(error);
  }
);

export const fetcher = async <T = any>(url: string): Promise<T> => {
  const accessToken = snapshot(accessTokenStore).accessToken;

  if (!accessToken) {
    throw new Error('No access token available');
  }

  try {
    const response: ApiResponse<T> = await myApi.get(url, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    return response.data;
  } catch (error) {
    throw error; // Throw error for SWR to handle
  }
};
