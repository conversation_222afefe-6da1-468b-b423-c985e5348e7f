import { z } from 'zod';

export const InventoryCatFormSchema = z.object({
  name: z.string().min(5, 'Inventory category name is required'),
});

export const InventoryFormSchema = z.object({
  name: z.string().min(4, {
    message: 'Required',
  }),
  category: z.string({
    required_error: 'Required',
  }),
  unit: z.string({
    required_error: 'Required',
  }),
  minimumStock: z
    .string()
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: 'Enter a valid value',
    }),
  currentStock: z
    .string()
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: 'Enter a valid value',
    }),
});

export const SupplyEntryFormSchema = z.object({
  invoice: z.any().optional(),
  supplier: z.string().min(1, 'Supplier is required'),
  quantitySupplied: z.string().min(1, 'Quantity is required'),
  unitPrice: z.string().min(1, 'Unit price is required'),
  totalAmount: z.string().optional(),
});

export const IssueIItemFormSchema = z.object({
  quantity: z.string().min(1, 'Quantity is required'),
  comment: z.string().min(10, 'Comment is required'),
});


export const MenuFormSchema = z.object({
  category: z.string({
    required_error: 'Required',
  }),
  name: z.string().min(3, 'Required'),
  general: z.string().min(3, 'Required'),
  staff: z.string().min(3, 'Required'),
});

export type Category = z.infer<typeof InventoryCatFormSchema>;
export type Inventory = z.infer<typeof InventoryFormSchema>;
export type SupplyEntry = z.infer<typeof SupplyEntryFormSchema>;
export type IssueEntry = z.infer<typeof IssueIItemFormSchema>;
export type MenuValue = z.infer<typeof MenuFormSchema>;
