'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Minus,
  ShoppingCart,
  Trash2,
  Users,
  UserRound,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PaymentModal from './PaymentModal';

// Mock menu items - in a real app, these would come from an API or database
const menuItems = [
  // General menu items
  {
    id: 1,
    name: 'Burger',
    price: 8.99,
    category: 'Main',
    image: '/placeholder.png',
    saleType: 'general',
  },
  {
    id: 2,
    name: 'Pizza',
    price: 10.99,
    category: 'Main',
    image: '/placeholder.png',
    saleType: 'general',
  },
  {
    id: 3,
    name: 'Salad',
    price: 6.99,
    category: 'Side',
    image: '/placeholder.png',
    saleType: 'general',
  },
  {
    id: 4,
    name: 'Soda',
    price: 2.49,
    category: 'Drink',
    image: '/placeholder.png',
    saleType: 'general',
  },
  {
    id: 5,
    name: 'Coffee',
    price: 3.49,
    category: 'Drink',
    image: '/placeholder.png',
    saleType: 'general',
  },
  {
    id: 6,
    name: 'Fries',
    price: 3.99,
    category: 'Side',
    image: '/placeholder.png',
    saleType: 'general',
  },

  // Staff menu items
  {
    id: 101,
    name: 'Staff Meal',
    price: 5.0,
    category: 'Staff',
    image: '/placeholder.png',
    saleType: 'staff',
  },
  {
    id: 102,
    name: 'Premium Coffee',
    price: 2.0,
    category: 'Drink',
    image: '/placeholder.png',
    saleType: 'staff',
  },
  {
    id: 103,
    name: 'Protein Bowl',
    price: 7.5,
    category: 'Main',
    image: '/placeholder.png',
    saleType: 'staff',
  },
  {
    id: 104,
    name: 'Fruit Salad',
    price: 3.5,
    category: 'Side',
    image: '/placeholder.png',
    saleType: 'staff',
  },

  // Other menu items
  {
    id: 201,
    name: 'Special Platter',
    price: 12.99,
    category: 'Special',
    image: '/placeholder.png',
    saleType: 'other',
  },
  {
    id: 202,
    name: 'Event Catering',
    price: 25.0,
    category: 'Event',
    image: '/placeholder.png',
    saleType: 'other',
  },
  {
    id: 203,
    name: 'Dessert Tray',
    price: 15.0,
    category: 'Dessert',
    image: '/placeholder.png',
    saleType: 'other',
  },
  {
    id: 204,
    name: 'Beverage Package',
    price: 8.0,
    category: 'Drink',
    image: '/placeholder.png',
    saleType: 'other',
  },
];

type CartItem = {
  id: number;
  name: string;
  price: number;
  quantity: number;
};

export default function SalesManagement() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >(null);
  const [saleType, setSaleType] = useState<'general' | 'staff'>('staff');

  // Update filter when sale type changes
  useEffect(() => {
    setSaleType(saleType === 'general' ? 'general' : saleType);
  }, [saleType]);

  // Filter menu items based on sale type and filter
  const currentMenuItems = menuItems.filter((item) => {
    if (saleType === 'general')
      return item.saleType === 'general' || item.saleType === 'staff';
    return item.saleType === saleType;
  });

  // Filter menu items based on search term and active category
  const filteredMenuItems = currentMenuItems.filter((item) => {
    const matchesSearch = item.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesCategory = activeCategory
      ? item.category === activeCategory
      : true;
    return matchesSearch && matchesCategory;
  });

  // Get unique categories for filter buttons
  const categories = Array.from(
    new Set(currentMenuItems.map((item) => item.category))
  );

  // Reset category filter when sale type changes
  // useEffect(() => {
  //   setActiveCategory(null);
  //   setCart([]);
  // }, [saleType]);

  // Add item to cart
  const addToCart = (item: { id: number; name: string; price: number }) => {
    setCart((prevCart) => {
      const existingItem = prevCart.find((cartItem) => cartItem.id === item.id);
      if (existingItem) {
        return prevCart.map((cartItem) =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prevCart, { ...item, quantity: 1 }];
      }
    });
  };

  // Update item quantity in cart
  const updateQuantity = (id: number, change: number) => {
    setCart((prevCart) => {
      return prevCart
        .map((item) => {
          if (item.id === id) {
            const newQuantity = Math.max(0, item.quantity + change);
            return { ...item, quantity: newQuantity };
          }
          return item;
        })
        .filter((item) => item.quantity > 0);
    });
  };

  // Calculate total price
  const totalPrice = cart.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  // Handle payment completion
  const handlePaymentComplete = () => {
    if (selectedPaymentMethod) {
      // In a real app, this would process the payment and create an order
      setCart([]);
      setPaymentModalOpen(false);
      setSelectedPaymentMethod(null);
    }
  };

  return (
    <>
      <div className="flex justify-center mb-4">
        <Tabs
          value={saleType}
          onValueChange={(value) => setSaleType(value as 'general' | 'staff')}
          className="w-1/2"
        >
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="staff" className="flex items-center gap-2">
              <UserRound className="w-4 h-4" />
              Staff
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Menu Items Section */}
        <div className="md:col-span-2 space-y-4">
          <div className="flex flex-col space-y-4">
            <div className="flex gap-4">
              <Input
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeCategory === null ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveCategory(null)}
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={activeCategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMenuItems.length === 0 && (
              <div className="col-span-3 text-center py-8 text-gray-500">
                No menu items available for {saleType} sale type
              </div>
            )}
            {filteredMenuItems.map((item) => (
              <Card
                key={item.id}
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => addToCart(item)}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{item.name}</h3>
                      <Badge variant="outline" className="mt-1">
                        {item.category}
                      </Badge>
                    </div>
                    <span className="font-bold">${item.price.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Cart Section */}
        <div className="md:col-span-1">
          <Card className="sticky top-4">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  Cart
                </h3>
                {cart.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCart([])}
                  >
                    Clear All
                  </Button>
                )}
              </div>

              {cart.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Your cart is empty
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map((item) => (
                    <div
                      key={item.id}
                      className="flex justify-between items-center py-2 border-b"
                    >
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">
                          ${item.price.toFixed(2)} × {item.quantity}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, -1)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-6 text-center">{item.quantity}</span>
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8 text-red-500"
                          onClick={() =>
                            updateQuantity(item.id, -item.quantity)
                          }
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  <div className="pt-4 border-t">
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>${totalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <div className="p-4 border-t">
              <Button
                className="w-full"
                disabled={cart.length === 0}
                onClick={() => setPaymentModalOpen(true)}
              >
                Complete Sale
              </Button>
            </div>

            {/* Payment Modal */}
            <PaymentModal
              open={paymentModalOpen}
              onOpenChange={setPaymentModalOpen}
              totalPrice={totalPrice}
              saleType={saleType}
              selectedPaymentMethod={selectedPaymentMethod}
              setSelectedPaymentMethod={setSelectedPaymentMethod}
              onComplete={handlePaymentComplete}
            />
          </Card>
        </div>
      </div>
    </>
  );
}
