'use client';

import { useState } from 'react';
import {
  CreditCard,
  Wallet,
  ArrowRightLeft,
  Star,
  CheckCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/common/modal';

interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  totalPrice: number;
  saleType: 'general' | 'staff';
  selectedPaymentMethod: string | null;
  setSelectedPaymentMethod: (method: string | null) => void;
  onComplete: () => void;
}

export default function PaymentModal({
  open,
  onOpenChange,
  totalPrice,
  saleType,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  onComplete,
}: PaymentModalProps) {
  const [staffId, setStaffId] = useState('');
  const [isStaffVerified, setIsStaffVerified] = useState(false);

  // Simple verification function - in a real app, this would call an API
  const verifyStaff = () => {
    // Mock verification - any non-empty ID is considered valid
    if (staffId.trim()) {
      setIsStaffVerified(true);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={onOpenChange}
      title="Select Payment Method"
      description=""
      onSubmit={undefined}
      isLoading={false}
      size="sm"
    >
      <div className="space-y-6">
        {saleType === 'staff' && !isStaffVerified ? (
          <div className="flex flex-col gap-4 py-4">
            <p className="text-sm text-gray-500">
              Please verify staff ID to continue
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Enter Staff ID"
                value={staffId}
                onChange={(e) => setStaffId(e.target.value)}
                className="flex-1"
              />
              <Button onClick={verifyStaff} disabled={!staffId.trim()}>
                Verify
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              variant={
                selectedPaymentMethod === 'transfer' ? 'default' : 'outline'
              }
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('transfer')}
            >
              <ArrowRightLeft className="h-8 w-8" />
              <span>Transfer</span>
            </Button>
            <Button
              variant={selectedPaymentMethod === 'card' ? 'default' : 'outline'}
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('card')}
            >
              <CreditCard className="h-8 w-8" />
              <span>Card</span>
            </Button>
            {saleType === 'staff' && (
              <>
                <Button
                  variant={
                    selectedPaymentMethod === 'wallet' ? 'default' : 'outline'
                  }
                  className="flex flex-col items-center justify-center h-24 gap-2"
                  onClick={() => setSelectedPaymentMethod('wallet')}
                >
                  <Wallet className="h-8 w-8" />
                  <span>Wallet</span>
                </Button>
                <Button
                  variant={
                    selectedPaymentMethod === 'credit' ? 'default' : 'outline'
                  }
                  className="flex flex-col items-center justify-center h-24 gap-2"
                  onClick={() => setSelectedPaymentMethod('credit')}
                >
                  <Star className="h-8 w-8" />
                  <span>Credit</span>
                </Button>
              </>
            )}
          </div>
        )}

        <div className="flex justify-between items-center py-2 border-t border-b">
          <span className="font-bold">Total:</span>
          <span className="font-bold text-lg">${totalPrice.toFixed(2)}</span>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {(saleType !== 'staff' || isStaffVerified) && (
            <Button disabled={!selectedPaymentMethod} onClick={onComplete}>
              Complete Payment
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
