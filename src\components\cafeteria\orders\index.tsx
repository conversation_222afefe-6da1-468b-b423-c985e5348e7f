'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  ShoppingCart,
  Search,
  Eye,
  CheckCircle,
  XCircle,
  Printer,
} from 'lucide-react';

interface OrdersManagementProps {
  orderType: 'general' | 'staff' | 'special';
}

export default function OrdersManagement({ orderType }: OrdersManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Mock orders data based on order type
  const getOrdersData = () => {
    const generalOrders = [
      {
        id: 'ORD-001',
        customer: '<PERSON>',
        items: 3,
        total: 15.99,
        status: 'Pending',
        time: '10:30 AM',
        date: '2023-06-16',
      },
      {
        id: 'ORD-002',
        customer: '<PERSON>',
        items: 2,
        total: 12.5,
        status: 'Completed',
        time: '11:15 AM',
        date: '2023-06-16',
      },
      {
        id: 'ORD-003',
        customer: 'Mike <PERSON>',
        items: 4,
        total: 22.75,
        status: 'In Progress',
        time: '12:00 PM',
        date: '2023-06-16',
      },
    ];

    const staffOrders = [
      {
        id: 'ORD-004',
        customer: 'Dr. <PERSON> <PERSON>',
        items: 2,
        total: 10.99,
        status: 'Pending',
        time: '09:45 AM',
        date: '2023-06-16',
      },
      {
        id: 'ORD-005',
        customer: 'Nurse David Brown',
        items: 1,
        total: 5.5,
        status: 'Completed',
        time: '10:20 AM',
        date: '2023-06-16',
      },
    ];

    const specialOrders = [
      {
        id: 'ORD-006',
        customer: 'VIP Guest',
        items: 5,
        total: 45.0,
        status: 'In Progress',
        time: '01:30 PM',
        date: '2023-06-16',
      },
      {
        id: 'ORD-007',
        customer: 'Executive Meeting',
        items: 10,
        total: 120.5,
        status: 'Pending',
        time: '02:00 PM',
        date: '2023-06-16',
      },
    ];

    switch (orderType) {
      case 'staff':
        return staffOrders;
      case 'special':
        return specialOrders;
      default:
        return generalOrders;
    }
  };

  const orders = getOrdersData();

  const filteredOrders = orders.filter(
    (order) =>
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.status.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusVariant = (
    status: string
  ): 'default' | 'secondary' | 'destructive' | 'outline' | 'success' => {
    switch (status) {
      case 'Completed':
        return 'success';
      case 'Pending':
        return 'secondary';
      case 'In Progress':
        return 'default';
      default:
        return 'outline';
    }
  };

  // POS integration section
  // const renderPOSIntegration = () => {
  //   return (
  //     <Card className="mb-6">
  //       <CardHeader>
  //         <CardTitle className="text-md">POS Integration</CardTitle>
  //       </CardHeader>
  //       <CardContent>
  //         <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
  //           <Button variant="outline" className="flex items-center gap-2">
  //             <Printer className="h-4 w-4" />
  //             Connect to POS Terminal
  //           </Button>
  //           <Button variant="outline" className="flex items-center gap-2">
  //             <ShoppingCart className="h-4 w-4" />
  //             Sync Orders
  //           </Button>
  //           <Button variant="outline" className="flex items-center gap-2">
  //             <Eye className="h-4 w-4" />
  //             View POS Analytics
  //           </Button>
  //         </div>
  //       </CardContent>
  //     </Card>
  //   );
  // };

  return (
    <div className="space-y-4">
      {/* POS Integration Section */}
      {/* {renderPOSIntegration()} */}

      <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search orders..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="text-sm font-medium">
          Showing {orderType.charAt(0).toUpperCase() + orderType.slice(1)}{' '}
          Orders
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Pending Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {orders.filter((order) => order.status === 'Pending').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Completed Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {orders.filter((order) => order.status === 'Completed').length}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Items</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.customer}</TableCell>
                  <TableCell>{order.items}</TableCell>
                  <TableCell>${order.total.toFixed(2)}</TableCell>
                  <TableCell>{order.time}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(order.status)}>
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No orders found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
