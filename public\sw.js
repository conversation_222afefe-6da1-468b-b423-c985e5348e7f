if (!self.define) {
  let e,
    n = {};
  const t = (t, s) => (
    (t = new URL(t + '.js', s).href),
    n[t] ||
      new Promise((n) => {
        if ('document' in self) {
          const e = document.createElement('script');
          (e.src = t), (e.onload = n), document.head.appendChild(e);
        } else (e = t), importScripts(t), n();
      }).then(() => {
        let e = n[t];
        if (!e) throw new Error(`Module ${t} didn’t register its module`);
        return e;
      })
  );
  self.define = (s, a) => {
    const i =
      e ||
      ('document' in self ? document.currentScript.src : '') ||
      location.href;
    if (n[i]) return;
    let c = {};
    const o = (e) => t(e, i),
      r = { module: { uri: i }, exports: c, require: o };
    n[i] = Promise.all(s.map((e) => r[e] || o(e))).then((e) => (a(...e), c));
  };
}
define(['./workbox-1bb06f5e'], function (e) {
  'use strict';
  importScripts(),
    self.skipWaiting(),
    e.clientsClaim(),
    e.precacheAndRoute(
      [
        {
          url: '/_next/app-build-manifest.json',
          revision: '3e7e168651ff4297d3e63ff674866ce7',
        },
        {
          url: '/_next/static/chunks/1044-989d46e3553a5261.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/1078-5dc9f867a616958f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/1684-2024d14f0d42f258.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/186-8d1910733644a6fb.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/1887-04d958d6754f21f1.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/2289-16be7c6a769e800d.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/2441-761faccc465dbbef.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/259-657f898f7d7739de.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/278-245d8b6ab7408f6c.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/2950-469b40ec3a61b8c1.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/2982-423d748aae90a6e0.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/3464-17e00d9ba44f28f7.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/3605-d01460ee32fa5c6f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/472.2c08b965bd9148e2.js',
          revision: '2c08b965bd9148e2',
        },
        {
          url: '/_next/static/chunks/4792-ce005be9015cfb5e.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/4945-4d0ef61a214c9d71.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/4bd1b696-9d7633aa5bd4fe4f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/5521-171c5b9d659d6c15.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/5766-baba0607c813113c.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6069-ead01464727496cf.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6240-8d04d68201d6dbcf.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6444-7bbdfe77bb423747.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6671-e6da9dd8068cdc87.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6730-52430d5a2d4b0a28.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6766-afa4461e8652e50a.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/6874-49b8f86bf7767823.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/7031-d56f181518827dd8.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/7039-17cbeba31885dc84.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/7096-220a5b5b39c8d4d3.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/8469-fc65f7730383acab.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/8617-f9054e3ce89dc253.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/8746-2973bd617dbcb545.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/879-a20ba80222509cab.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9055-638bc4e387ace6da.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9269-1d39f0d169beed69.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9293-790b0f29255542ae.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9341.c00e1bbc955787be.js',
          revision: 'c00e1bbc955787be',
        },
        {
          url: '/_next/static/chunks/9388-6169167ec77d674c.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9481-ce68daa3d6d4cfdb.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9519-918d92a362862377.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9638-8cd796d7ab183919.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/9669-8c01f9c221d8265d.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/998-f38b4aef2b28a380.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/cafeteria/page-a4c2e80e06ab48f8.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/dashboard/page-2a1df8d93f0a9f95.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/data-analysis/page-3ac2fda437875352.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/discount-code/page-d5f98e3d58d70960.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/page-409a61bbd99c82fc.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/feedbacks/page-cd82e50fa5cf442f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/direct-messages/page-80a3e3633d1fa41f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/groups/%5BgroupId%5D/page-c7430a34c21be1ce.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/page-968dd9731b34d214.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/incident-reporting/page-140593da776eb55e.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/layout-e8c285bec0329199.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/locations/page-facf4969c9c0672d.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/manage-staffs/page-cb9c0fc8ae6f1b8f.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/edit-package/%5Bslug%5D/page-11c12788c8faa240.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/investigation/page-78e73e4a7b3603a6.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-category/page-6936fe386da9dfac.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-package/page-500a8b6899cef06b.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/new-package/page-4d0908a8ee931d67.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/page-ef98a2bee9149721.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/analytics/page-eee928f350f97b07.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/appointments/page-f4cc29ce32869626.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/departments/page-b8ad5b33806e0abf.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/doctors/page-d37935bdaacee140.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/interactions/page-b8c8a9d5aab9abb5.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/medical-records/page-992ce0cf0a1fc4c3.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/page-f5e6dd161afa4b3d.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/patients/page-7ff77f367d32fa8d.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/page-7e304e748b7d6afc.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/referring-entities/page-4a540c5a6fc9ba70.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/rewards/page-3e6164c0ef6cd0e6.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/rewards/page-473158b706e037e0.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/system-settings/page-1efc96cbe5d45644.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/transactions/page-fa801d40052fed40.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/_not-found/page-72fa00e5a996b855.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/error-6492789314180dea.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/forbidden/page-c8ecdc5976d4f32a.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/layout-5571dfe0c0febb6a.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/login/page-d3bb5983c6f8747c.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/app/page-73c79a8d27ba3dbe.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/framework-c054b661e612b06c.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/main-app-15ab57bde25404f0.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/main-bf16eb4740561336.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/pages/_app-4e5feef8afe3c684.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/pages/_error-faae17b7f45ea4ac.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/chunks/polyfills-42372ed130431b0a.js',
          revision: '846118c33b2c0e922d7b3a7676f81f6f',
        },
        {
          url: '/_next/static/chunks/webpack-585805a6031f1a0a.js',
          revision: 'o0no7fXtp7bFy9yj5VnCD',
        },
        {
          url: '/_next/static/css/63247c90112e98bf.css',
          revision: '63247c90112e98bf',
        },
        {
          url: '/_next/static/media/034d78ad42e9620c-s.woff2',
          revision: 'be7c930fceb794521be0a68e113a71d8',
        },
        {
          url: '/_next/static/media/29a4aea02fdee119-s.woff2',
          revision: '69d9d2cdadeab7225297d50fc8e48e8b',
        },
        {
          url: '/_next/static/media/4c285fdca692ea22-s.p.woff2',
          revision: '42d3308e3aca8742731f63154187bdd7',
        },
        {
          url: '/_next/static/media/6c177e25b87fd9cd-s.woff2',
          revision: '4f9434d4845212443bbd9d102f1f5d7d',
        },
        {
          url: '/_next/static/media/6c9a125e97d835e1-s.woff2',
          revision: '889718d692d5bfc6019cbdfcb5cc106f',
        },
        {
          url: '/_next/static/media/a1386beebedccca4-s.woff2',
          revision: 'd3aa06d13d3cf9c0558927051f3cb948',
        },
        {
          url: '/_next/static/media/b957ea75a84b6ea7-s.p.woff2',
          revision: '0bd523f6049956faaf43c254a719d06a',
        },
        {
          url: '/_next/static/media/eafabf029ad39a43-s.p.woff2',
          revision: '43751174b6b810eb169101a20d8c26f8',
        },
        {
          url: '/_next/static/media/fe0777f1195381cb-s.woff2',
          revision: 'f2a04185547c36abfa589651236a9849',
        },
        {
          url: '/_next/static/media/icon.0c4b6864.png',
          revision: '6c32b251eefc1a2fe5730ea8f1cda7fb',
        },
        {
          url: '/_next/static/media/logo.7bb171e6.png',
          revision: 'c4ff5bcbd8371f70a6b4dee2b692f68c',
        },
        {
          url: '/_next/static/o0no7fXtp7bFy9yj5VnCD/_buildManifest.js',
          revision: '0f825844dd8ffe95c1830472983ff2e5',
        },
        {
          url: '/_next/static/o0no7fXtp7bFy9yj5VnCD/_ssgManifest.js',
          revision: 'b6652df95db52feb4daf4eca35380933',
        },
        { url: '/icon-512.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/icon.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/manifest.json', revision: '506f7804edbf5541ab40326a24d01d3d' },
        {
          url: '/whatsapp-bg.png',
          revision: '6264a93f7bf5f59d4512c2692a6f18e7',
        },
      ],
      { ignoreURLParametersMatching: [] }
    ),
    e.cleanupOutdatedCaches(),
    e.registerRoute(
      '/',
      new e.NetworkFirst({
        cacheName: 'start-url',
        plugins: [
          {
            cacheWillUpdate: async ({
              request: e,
              response: n,
              event: t,
              state: s,
            }) =>
              n && 'opaqueredirect' === n.type
                ? new Response(n.body, {
                    status: 200,
                    statusText: 'OK',
                    headers: n.headers,
                  })
                : n,
          },
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,
      new e.CacheFirst({
        cacheName: 'google-fonts-webfonts',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 31536e3 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,
      new e.StaleWhileRevalidate({
        cacheName: 'google-fonts-stylesheets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-font-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-image-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/image\?url=.+$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-image',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp3|wav|ogg)$/i,
      new e.CacheFirst({
        cacheName: 'static-audio-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp4)$/i,
      new e.CacheFirst({
        cacheName: 'static-video-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:js)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-js-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:css|less)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-style-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/data\/.+\/.+\.json$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-data',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:json|xml|csv)$/i,
      new e.NetworkFirst({
        cacheName: 'static-data-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        const n = e.pathname;
        return !n.startsWith('/api/auth/') && !!n.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'apis',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 16, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        return !e.pathname.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'others',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => !(self.origin === e.origin),
      new e.NetworkFirst({
        cacheName: 'cross-origin',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 3600 }),
        ],
      }),
      'GET'
    );
});
