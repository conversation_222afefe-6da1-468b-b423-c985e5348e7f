import Layout from '@/components/common/layout';
import { NotificationPermission } from '@/components/notification-permission';
import WebSocketProvider from '@/Providers/WebSocketProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <WebSocketProvider>
      <Layout>
        <main>{children}</main>
        <NotificationPermission />
      </Layout>
    </WebSocketProvider>
  );
}
