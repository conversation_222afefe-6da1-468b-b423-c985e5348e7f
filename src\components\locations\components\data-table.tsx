import React, { useState } from 'react';
import dayjs from 'dayjs';
import { Loader2, Ellipsis } from 'lucide-react';
import { GetLocations } from '@/api/data';
import { StatusBadge } from '@/components/common/status-badge';
import NewLocation from './addModal';
import { EmptyState, LoadingState } from '@/components/common/dataState';

interface LocationProps {
  openCreate: boolean;
  permitEdit: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const Location: React.FC<LocationProps> = ({
  openCreate,
  permitEdit,
  setOpenCreate,
}) => {
  const [open, setOpen] = useState(false);
  const [detail, setDetail] = useState<any | null>(null);

  const { locations, isLoading, mutate } = GetLocations();
  const data = locations?.data;

  const handleEventFromModal = (category: any) => {
    setDetail(category);
    setOpen(true);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Name</th>
              <th className="table-style">Region</th>
              <th className="table-style">Country</th>
              <th className="table-style">Currency</th>
              <th className="table-style">Longitude</th>
              <th className="table-style">Latitude</th>
              <th className="table-style">Status</th>
              <th className="table-style">Edit</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {data?.map((location: any, index: number) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={location.id}
              >
                <td className="table-style">{index + 1}</td>
                <td className="table-style">
                  {dayjs(location.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{location.name}</td>
                <td className="table-style">{location.region}</td>
                <td className="table-style">{location.country}</td>
                <td className="table-style">{location.currency}</td>
                <td className="table-style">
                  {location.longitude ? location.longitude : '-'}
                </td>
                <td className="table-style">
                  {location.latitude ? location.latitude : '-'}
                </td>
                <td className="table-style">
                  {location.isActive
                    ? StatusBadge({ status: 'active' })
                    : StatusBadge({ status: 'inactive' })}
                </td>
                <td className="table-style">
                  {permitEdit ? (
                    <Ellipsis
                      onClick={() => handleEventFromModal(location)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  ) : (
                    <Ellipsis className="w-4 h-4 text-gray-400" />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {isLoading ? (
          <LoadingState />
        ) : data?.lenght === 0 ? (
          <EmptyState />
        ) : null}
      </div>
      {/* <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} /> */}
      <NewLocation open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </>
  );
};

export default Location;
